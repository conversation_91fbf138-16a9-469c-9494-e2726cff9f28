"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/thresholds/route";
exports.ids = ["app/api/thresholds/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fthresholds%2Froute&page=%2Fapi%2Fthresholds%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fthresholds%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fthresholds%2Froute&page=%2Fapi%2Fthresholds%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fthresholds%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_workspaces_nsl_back_SrsrMan_backend_app_api_thresholds_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/thresholds/route.ts */ \"(rsc)/./app/api/thresholds/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/thresholds/route\",\n        pathname: \"/api/thresholds\",\n        filename: \"route\",\n        bundlePath: \"app/api/thresholds/route\"\n    },\n    resolvedPagePath: \"D:\\\\workspaces\\\\nsl\\\\back\\\\SrsrMan\\\\backend\\\\app\\\\api\\\\thresholds\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_workspaces_nsl_back_SrsrMan_backend_app_api_thresholds_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/thresholds/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fthresholds%2Froute&page=%2Fapi%2Fthresholds%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fthresholds%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/thresholds/route.ts":
/*!*************************************!*\
  !*** ./app/api/thresholds/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/validation */ \"(rsc)/./lib/validation.ts\");\n/* harmony import */ var joi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! joi */ \"(rsc)/./node_modules/joi/lib/index.js\");\n/* harmony import */ var joi__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(joi__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst createThresholdSchema = joi__WEBPACK_IMPORTED_MODULE_4___default().object({\n    service_type: joi__WEBPACK_IMPORTED_MODULE_4___default().string().required(),\n    metric_name: joi__WEBPACK_IMPORTED_MODULE_4___default().string().required(),\n    warning_threshold: joi__WEBPACK_IMPORTED_MODULE_4___default().number().required(),\n    critical_threshold: joi__WEBPACK_IMPORTED_MODULE_4___default().number().required(),\n    unit: joi__WEBPACK_IMPORTED_MODULE_4___default().string().required(),\n    description: joi__WEBPACK_IMPORTED_MODULE_4___default().string().optional(),\n    is_active: joi__WEBPACK_IMPORTED_MODULE_4___default().boolean().default(true)\n});\nasync function getThresholdsHandler(request, context, currentUser) {\n    try {\n        const params = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getQueryParams)(request);\n        const { service_type, metric_name, is_active } = params;\n        // Build where clause\n        const where = {};\n        if (service_type) {\n            where.serviceType = service_type;\n        }\n        if (metric_name) {\n            where.metricName = metric_name;\n        }\n        if (is_active !== undefined) {\n            where.isActive = is_active === \"true\";\n        }\n        // Get threshold configurations\n        const thresholds = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.thresholdConfig.findMany({\n            where,\n            orderBy: [\n                {\n                    serviceType: \"asc\"\n                },\n                {\n                    metricName: \"asc\"\n                }\n            ]\n        });\n        // Transform data to match API response format\n        const transformedThresholds = thresholds.map((threshold)=>({\n                id: threshold.id,\n                service_type: threshold.serviceType,\n                metric_name: threshold.metricName,\n                warning_threshold: threshold.warningThreshold,\n                critical_threshold: threshold.criticalThreshold,\n                unit: threshold.unit,\n                description: threshold.description,\n                is_active: threshold.isActive,\n                created_at: threshold.createdAt,\n                updated_at: threshold.updatedAt\n            }));\n        return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(transformedThresholds), {\n            status: 200,\n            headers: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)()\n        });\n    } catch (error) {\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error, \"Failed to fetch threshold configurations\");\n    }\n}\nasync function createThresholdHandler(request, context, currentUser) {\n    try {\n        const body = await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getRequestBody)(request);\n        // Validate request body\n        const validation = (0,_lib_validation__WEBPACK_IMPORTED_MODULE_3__.validateRequest)(createThresholdSchema, body);\n        if (!validation.isValid) {\n            return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(null, \"Validation failed\", \"VALIDATION_ERROR\"), {\n                status: 400\n            });\n        }\n        const { service_type, metric_name, warning_threshold, critical_threshold, unit, description, is_active } = validation.data;\n        // Check if threshold already exists for this service type and metric\n        const existingThreshold = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.thresholdConfig.findFirst({\n            where: {\n                serviceType: service_type,\n                metricName: metric_name\n            }\n        });\n        if (existingThreshold) {\n            return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(null, \"Threshold configuration already exists for this service type and metric\", \"DUPLICATE_THRESHOLD\"), {\n                status: 409\n            });\n        }\n        // Create threshold configuration\n        const threshold = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.thresholdConfig.create({\n            data: {\n                serviceType: service_type,\n                metricName: metric_name,\n                warningThreshold: warning_threshold,\n                criticalThreshold: critical_threshold,\n                unit,\n                description,\n                isActive: is_active\n            }\n        });\n        return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)({\n            message: \"Threshold configuration created successfully\",\n            threshold: {\n                id: threshold.id,\n                service_type: threshold.serviceType,\n                metric_name: threshold.metricName,\n                warning_threshold: threshold.warningThreshold,\n                critical_threshold: threshold.criticalThreshold,\n                unit: threshold.unit,\n                description: threshold.description,\n                is_active: threshold.isActive,\n                created_at: threshold.createdAt,\n                updated_at: threshold.updatedAt\n            }\n        }), {\n            status: 201,\n            headers: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)()\n        });\n    } catch (error) {\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error, \"Failed to create threshold configuration\");\n    }\n}\nconst GET = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.requireAuth)(getThresholdsHandler);\nconst POST = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.requireRole)([\n    \"admin\"\n])(createThresholdHandler);\nasync function OPTIONS() {\n    return new Response(null, {\n        status: 200,\n        headers: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/thresholds/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comparePassword: () => (/* binding */ comparePassword),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   getUserRoles: () => (/* binding */ getUserRoles),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"fallback-secret\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"7d\";\nfunction generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function comparePassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nasync function getUserRoles(userId) {\n    const userRoles = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.userRole.findMany({\n        where: {\n            userId\n        },\n        include: {\n            role: true\n        }\n    });\n    return userRoles.map((ur)=>ur.role.name);\n}\nasync function getAuthUser(request) {\n    try {\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return null;\n        }\n        const token = authHeader.substring(7);\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            select: {\n                id: true,\n                email: true,\n                fullName: true,\n                phone: true,\n                isActive: true,\n                createdAt: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        // Get user roles\n        const roles = await getUserRoles(user.id);\n        return {\n            ...user,\n            roles\n        };\n    } catch (error) {\n        return null;\n    }\n}\nfunction requireAuth(handler) {\n    return async (request, context)=>{\n        const user = await getAuthUser(request);\n        if (!user) {\n            return Response.json({\n                success: false,\n                error: \"Unauthorized\",\n                code: \"UNAUTHORIZED\"\n            }, {\n                status: 401\n            });\n        }\n        return handler(request, context, user);\n    };\n}\nfunction requireRole(roles) {\n    return (handler)=>{\n        return async (request, context)=>{\n            const user = await getAuthUser(request);\n            if (!user) {\n                return Response.json({\n                    success: false,\n                    error: \"Unauthorized\",\n                    code: \"UNAUTHORIZED\"\n                }, {\n                    status: 401\n                });\n            }\n            const hasRole = roles.some((role)=>user.roles.includes(role));\n            if (!hasRole) {\n                return Response.json({\n                    success: false,\n                    error: \"Insufficient permissions\",\n                    code: \"FORBIDDEN\"\n                }, {\n                    status: 403\n                });\n            }\n            return handler(request, context, user);\n        };\n    };\n}\nasync function hasPermission(userId, resource, action) {\n    const userRoles = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.userRole.findMany({\n        where: {\n            userId\n        },\n        include: {\n            role: {\n                include: {\n                    rolePermissions: {\n                        include: {\n                            permission: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n    for (const userRole of userRoles){\n        for (const rolePermission of userRole.role.rolePermissions){\n            const permission = rolePermission.permission;\n            if (permission.resource === resource && permission.action === action) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\nfunction requirePermission(resource, action) {\n    return (handler)=>{\n        return async (request, context)=>{\n            const user = await getAuthUser(request);\n            if (!user) {\n                return Response.json({\n                    success: false,\n                    error: \"Unauthorized\",\n                    code: \"UNAUTHORIZED\"\n                }, {\n                    status: 401\n                });\n            }\n            const hasAccess = await hasPermission(user.id, resource, action);\n            if (!hasAccess) {\n                return Response.json({\n                    success: false,\n                    error: \"Insufficient permissions\",\n                    code: \"FORBIDDEN\"\n                }, {\n                    status: 403\n                });\n            }\n            return handler(request, context, user);\n        };\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUU5QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFHO0FBRW5FLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Nyc3ItcHJvcGVydHktbWFuYWdlbWVudC1iYWNrZW5kLy4vbGliL3ByaXNtYS50cz85ODIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   corsHeaders: () => (/* binding */ corsHeaders),\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   createPaginationResponse: () => (/* binding */ createPaginationResponse),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   getQueryParams: () => (/* binding */ getQueryParams),\n/* harmony export */   getRequestBody: () => (/* binding */ getRequestBody),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   parseDate: () => (/* binding */ parseDate)\n/* harmony export */ });\nfunction createApiResponse(data, error, code) {\n    return {\n        success: !error,\n        ...data && {\n            data\n        },\n        ...error && {\n            error\n        },\n        ...code && {\n            code\n        }\n    };\n}\nfunction createPaginationResponse(data, page, limit, total) {\n    const pages = Math.ceil(total / limit);\n    return {\n        success: true,\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            pages,\n            has_next: page < pages,\n            has_prev: page > 1\n        }\n    };\n}\nfunction getQueryParams(request) {\n    const { searchParams } = new URL(request.url);\n    const params = {};\n    searchParams.forEach((value, key)=>{\n        // Handle numeric values\n        if (!isNaN(Number(value))) {\n            params[key] = Number(value);\n        } else if (value === \"true\" || value === \"false\") {\n            // Handle boolean values\n            params[key] = value === \"true\";\n        } else {\n            params[key] = value;\n        }\n    });\n    return params;\n}\nasync function getRequestBody(request) {\n    try {\n        return await request.json();\n    } catch (error) {\n        return null;\n    }\n}\nfunction handleError(error, defaultMessage = \"Internal server error\", context) {\n    console.error(`API Error${context ? ` (${context})` : \"\"}:`, error);\n    // Rate limiting errors (from V1 patterns)\n    if (isRateLimitError(error)) {\n        return Response.json(createApiResponse(null, \"Rate limit exceeded. Please try again in a moment.\", \"RATE_LIMIT_EXCEEDED\"), {\n            status: 429,\n            headers: {\n                ...corsHeaders(),\n                \"Retry-After\": \"60\"\n            }\n        });\n    }\n    // JSON parsing errors (often related to rate limiting)\n    if (isJsonParsingError(error)) {\n        return Response.json(createApiResponse(null, \"Request parsing failed. Please try again.\", \"PARSING_ERROR\"), {\n            status: 400,\n            headers: corsHeaders()\n        });\n    }\n    // Database constraint errors\n    if (error.code === \"P2002\") {\n        const field = error.meta?.target?.[0] || \"field\";\n        return Response.json(createApiResponse(null, `${field} already exists`, \"DUPLICATE_ENTRY\"), {\n            status: 409,\n            headers: corsHeaders()\n        });\n    }\n    if (error.code === \"P2025\") {\n        return Response.json(createApiResponse(null, \"Resource not found\", \"NOT_FOUND\"), {\n            status: 404,\n            headers: corsHeaders()\n        });\n    }\n    // Database connection errors\n    if (error.code === \"P1001\" || error.code === \"P1008\") {\n        return Response.json(createApiResponse(null, \"Database connection failed. Please try again.\", \"DATABASE_ERROR\"), {\n            status: 503,\n            headers: corsHeaders()\n        });\n    }\n    // Validation errors\n    if (error.name === \"ValidationError\" || error.isJoi) {\n        return Response.json(createApiResponse(null, error.message || \"Validation failed\", \"VALIDATION_ERROR\"), {\n            status: 400,\n            headers: corsHeaders()\n        });\n    }\n    // Network/timeout errors\n    if (error.code === \"ECONNRESET\" || error.code === \"ETIMEDOUT\") {\n        return Response.json(createApiResponse(null, \"Network error. Please try again.\", \"NETWORK_ERROR\"), {\n            status: 503,\n            headers: corsHeaders()\n        });\n    }\n    return Response.json(createApiResponse(null, defaultMessage, \"INTERNAL_ERROR\"), {\n        status: 500,\n        headers: corsHeaders()\n    });\n}\n// Helper functions for error detection (from V1 patterns)\nfunction isRateLimitError(error) {\n    const message = (error?.message || \"\").toLowerCase();\n    return message.includes(\"too many requests\") || message.includes(\"rate limit\") || message.includes(\"429\") || error?.response?.status === 429;\n}\nfunction isJsonParsingError(error) {\n    const message = (error?.message || \"\").toLowerCase();\n    return error instanceof SyntaxError || message.includes(\"unexpected token\") || message.includes(\"json\") || message.includes(\"syntaxerror\");\n}\nfunction corsHeaders() {\n    return {\n        \"Access-Control-Allow-Origin\": \"*\",\n        \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n        \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n    };\n}\n// Date utilities\nfunction formatDate(date) {\n    return date.toISOString().split(\"T\")[0];\n}\nfunction formatDateTime(date) {\n    return date.toISOString();\n}\nfunction parseDate(dateString) {\n    return new Date(dateString);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/validation.ts":
/*!***************************!*\
  !*** ./lib/validation.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createFuelLogSchema: () => (/* binding */ createFuelLogSchema),\n/* harmony export */   createMaintenanceIssueSchema: () => (/* binding */ createMaintenanceIssueSchema),\n/* harmony export */   createPropertySchema: () => (/* binding */ createPropertySchema),\n/* harmony export */   legacyLoginSchema: () => (/* binding */ legacyLoginSchema),\n/* harmony export */   loginSchema: () => (/* binding */ loginSchema),\n/* harmony export */   paginationSchema: () => (/* binding */ paginationSchema),\n/* harmony export */   registerSchema: () => (/* binding */ registerSchema),\n/* harmony export */   submitAttendanceSchema: () => (/* binding */ submitAttendanceSchema),\n/* harmony export */   validateRequest: () => (/* binding */ validateRequest)\n/* harmony export */ });\n/* harmony import */ var joi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! joi */ \"(rsc)/./node_modules/joi/lib/index.js\");\n/* harmony import */ var joi__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(joi__WEBPACK_IMPORTED_MODULE_0__);\n\n// Auth validation schemas\nconst loginSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    identifier: joi__WEBPACK_IMPORTED_MODULE_0___default().string().required(),\n    password: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(6).required(),\n    login_type: joi__WEBPACK_IMPORTED_MODULE_0___default().string().valid(\"email\", \"username\", \"phone\").optional()\n});\nconst legacyLoginSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    email: joi__WEBPACK_IMPORTED_MODULE_0___default().string().email().required(),\n    password: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(6).required()\n});\nconst registerSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    email: joi__WEBPACK_IMPORTED_MODULE_0___default().string().email().required(),\n    username: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(3).max(50).pattern(/^[a-zA-Z0-9_-]+$/).optional(),\n    password: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(6).required(),\n    full_name: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(2).required(),\n    phone: joi__WEBPACK_IMPORTED_MODULE_0___default().string().pattern(/^[+]?[1-9]\\d{1,14}$/).optional()\n});\n// Property validation schemas\nconst createPropertySchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    name: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(2).required(),\n    type: joi__WEBPACK_IMPORTED_MODULE_0___default().string().valid(\"residential\", \"office\", \"construction_site\").required(),\n    parent_property_id: joi__WEBPACK_IMPORTED_MODULE_0___default().string().uuid().optional(),\n    address: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n    description: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n    // Office-specific fields\n    capacity: joi__WEBPACK_IMPORTED_MODULE_0___default().number().integer().min(1).when(\"type\", {\n        is: \"office\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().optional(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    department: joi__WEBPACK_IMPORTED_MODULE_0___default().string().when(\"type\", {\n        is: \"office\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().optional(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    // Site-specific fields\n    project_type: joi__WEBPACK_IMPORTED_MODULE_0___default().string().when(\"type\", {\n        is: \"construction_site\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().required(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    start_date: joi__WEBPACK_IMPORTED_MODULE_0___default().date().when(\"type\", {\n        is: \"construction_site\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().required(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    expected_end_date: joi__WEBPACK_IMPORTED_MODULE_0___default().date().when(\"type\", {\n        is: \"construction_site\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().required(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    hourly_rate_standard: joi__WEBPACK_IMPORTED_MODULE_0___default().number().positive().when(\"type\", {\n        is: \"construction_site\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().optional(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    // Common fields\n    location: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional()\n});\n// Maintenance validation schemas\nconst createMaintenanceIssueSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    property_id: joi__WEBPACK_IMPORTED_MODULE_0___default().string().uuid().required(),\n    title: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(2).required(),\n    description: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(5).required(),\n    priority: joi__WEBPACK_IMPORTED_MODULE_0___default().string().valid(\"low\", \"medium\", \"high\", \"critical\").required(),\n    service_type: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n    department: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n    due_date: joi__WEBPACK_IMPORTED_MODULE_0___default().date().optional()\n});\n// Attendance validation schemas\nconst submitAttendanceSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    date: joi__WEBPACK_IMPORTED_MODULE_0___default().date().required(),\n    attendance: joi__WEBPACK_IMPORTED_MODULE_0___default().array().items(joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n        worker_id: joi__WEBPACK_IMPORTED_MODULE_0___default().string().uuid().required(),\n        status: joi__WEBPACK_IMPORTED_MODULE_0___default().string().valid(\"present\", \"absent\", \"late\", \"half_day\").required(),\n        check_in_time: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n        check_out_time: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n        hours_worked: joi__WEBPACK_IMPORTED_MODULE_0___default().number().min(0).max(24).optional(),\n        notes: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional()\n    })).required()\n});\n// Generator fuel validation schemas\nconst createFuelLogSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    fuel_level_liters: joi__WEBPACK_IMPORTED_MODULE_0___default().number().min(0).required(),\n    consumption_rate: joi__WEBPACK_IMPORTED_MODULE_0___default().number().min(0).optional(),\n    runtime_hours: joi__WEBPACK_IMPORTED_MODULE_0___default().number().min(0).optional(),\n    efficiency_percentage: joi__WEBPACK_IMPORTED_MODULE_0___default().number().min(0).max(100).optional(),\n    notes: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional()\n});\n// Pagination validation\nconst paginationSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    page: joi__WEBPACK_IMPORTED_MODULE_0___default().number().integer().min(1).default(1),\n    limit: joi__WEBPACK_IMPORTED_MODULE_0___default().number().integer().min(1).max(100).default(10)\n});\n// Validation helper function\nfunction validateRequest(schema, data) {\n    const { error, value } = schema.validate(data, {\n        abortEarly: false\n    });\n    if (error) {\n        const errors = error.details.map((detail)=>({\n                field: detail.path.join(\".\"),\n                message: detail.message\n            }));\n        return {\n            isValid: false,\n            errors,\n            data: null\n        };\n    }\n    return {\n        isValid: true,\n        errors: null,\n        data: value\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/validation.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/joi","vendor-chunks/@sideway","vendor-chunks/@hapi"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fthresholds%2Froute&page=%2Fapi%2Fthresholds%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fthresholds%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();