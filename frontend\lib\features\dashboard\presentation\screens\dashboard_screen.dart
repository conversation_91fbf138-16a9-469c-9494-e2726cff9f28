import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../core/auth/widgets/permission_widget.dart';
import '../../../../core/permissions/configurable_permission_widget.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../shared/widgets/stat_card.dart';
import '../../../../features/properties/presentation/providers/properties_providers.dart';
import '../../../../features/maintenance/presentation/providers/maintenance_providers.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../providers/dashboard_providers.dart';
import '../widgets/recent_alerts_widget.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardAsync = ref.watch(dashboardDataProvider);
    final currentUserAsync = ref.watch(currentUserProvider);

    return Scaffold(
      appBar: RoleBasedAppBar(
        title: 'Dashboard',
        showRoleIndicator: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(dashboardDataProvider);
              ref.invalidate(propertiesProvider);
              ref.invalidate(maintenanceIssuesProvider);
            },
          ),
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () => context.push('/debug-dashboard'),
            tooltip: 'Debug Dashboard',
          ),
          PopupMenuButton<String>(
            itemBuilder: (context) {
              final items = <PopupMenuEntry<String>>[];

              // Profile item (always visible)
              items.add(const PopupMenuItem<String>(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person, size: 20),
                    SizedBox(width: 8),
                    Text('Profile'),
                  ],
                ),
              ));

              // Settings item (permission-based)
              final currentUser = ref.read(currentUserProvider);
              final hasSettingsPermission = currentUser.when(
                data: (user) => user?.hasPermission('settings.configure') ?? false,
                loading: () => false,
                error: (_, __) => false,
              );

              if (hasSettingsPermission) {
                items.add(const PopupMenuItem<String>(
                  value: 'settings',
                  child: Row(
                    children: [
                      Icon(Icons.settings, size: 20),
                      SizedBox(width: 8),
                      Text('Settings'),
                    ],
                  ),
                ));
              }

              // Admin item (role-based)
              final hasAdminRole = currentUser.when(
                data: (user) {
                  return user?.isAdmin ?? false;
                },
                loading: () => false,
                error: (_, __) => false,
              );

              if (hasAdminRole) {
                items.add(const PopupMenuItem<String>(
                  value: 'admin',
                  child: Row(
                    children: [
                      Icon(Icons.admin_panel_settings, size: 20),
                      SizedBox(width: 8),
                      Text('Admin Panel'),
                    ],
                  ),
                ));
              }

              // Divider and logout (always visible)
              items.add(const PopupMenuDivider());
              items.add(const PopupMenuItem<String>(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, size: 20),
                    SizedBox(width: 8),
                    Text('Logout'),
                  ],
                ),
              ));

              return items;
            },
            onSelected: (value) {
              switch (value) {
                case 'profile':
                  context.push('/profile');
                  break;
                case 'settings':
                  context.push('/settings');
                  break;
                case 'admin':
                  context.push('/admin');
                  break;
                case 'logout':
                  _handleLogout(ref);
                  break;
              }
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(dashboardDataProvider);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              currentUserAsync.when(
                data: (user) => user != null
                    ? Card(
                        child: Padding(
                          padding: const EdgeInsets.all(AppConstants.defaultPadding),
                          child: Row(
                            children: [
                              CircleAvatar(
                                radius: 30,
                                child: Text(
                                  user.fullName.isNotEmpty ? user.fullName[0].toUpperCase() : 'U',
                                  style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                                ),
                              ),
                              const SizedBox(width: AppConstants.defaultPadding),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Welcome back, ${user.fullName}!',
                                      style: Theme.of(context).textTheme.titleLarge,
                                    ),
                                    Text(
                                      user.email,
                                      style: Theme.of(context).textTheme.bodyMedium,
                                    ),
                                    if (user.roles.isNotEmpty)
                                      Wrap(
                                        spacing: 4,
                                        children: user.roles.map((role) => Chip(
                                          label: Text(AppUtils.capitalize(role)),
                                          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                        )).toList(),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
                loading: () => const Card(
                  child: Padding(
                    padding: EdgeInsets.all(AppConstants.defaultPadding),
                    child: Row(
                      children: [
                        CircleAvatar(radius: 30),
                        SizedBox(width: AppConstants.defaultPadding),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: 20, child: LinearProgressIndicator()),
                              SizedBox(height: 8),
                              SizedBox(height: 16, child: LinearProgressIndicator()),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                error: (_, __) => const SizedBox.shrink(),
              ),
              const SizedBox(height: AppConstants.defaultPadding),

              // Dashboard Content
              dashboardAsync.when(
                data: (dashboard) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Property Statistics
                    ConfigurablePermissionSection(
                      screenName: 'dashboard',
                      widgetName: 'property_stats',
                      title: 'Property Overview',
                      children: [
                        GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          crossAxisSpacing: AppConstants.smallPadding,
                          mainAxisSpacing: AppConstants.smallPadding,
                          childAspectRatio: 1.2,
                          children: [
                            StatCard(
                              title: 'Total Properties',
                              value: dashboard.data.properties.total.toString(),
                              icon: Icons.business,
                              color: Colors.blue,
                              onTap: () => context.push('/properties'),
                            ),
                            StatCard(
                              title: 'Operational',
                              value: dashboard.data.properties.operational.toString(),
                              icon: Icons.check_circle,
                              color: Colors.green,
                              onTap: () => context.push('/properties?status=operational'),
                            ),
                            StatCard(
                              title: 'Warning',
                              value: dashboard.data.properties.warning.toString(),
                              icon: Icons.warning,
                              color: Colors.orange,
                              onTap: () => context.push('/properties?status=warning'),
                            ),
                            StatCard(
                              title: 'Critical',
                              value: dashboard.data.properties.critical.toString(),
                              icon: Icons.error,
                              color: Colors.red,
                              onTap: () => context.push('/properties?status=critical'),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.largePadding),

                    // Maintenance Statistics
                    ConfigurablePermissionSection(
                      screenName: 'dashboard',
                      widgetName: 'maintenance_stats',
                      title: 'Maintenance Issues',
                      children: [
                        GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          crossAxisSpacing: AppConstants.smallPadding,
                          mainAxisSpacing: AppConstants.smallPadding,
                          childAspectRatio: 1.2,
                          children: [
                            StatCard(
                              title: 'Total Issues',
                              value: dashboard.data.maintenanceIssues.total.toString(),
                              icon: Icons.build,
                              color: Colors.blue,
                              onTap: () => context.push('/maintenance'),
                            ),
                            StatCard(
                              title: 'Open',
                              value: dashboard.data.maintenanceIssues.open.toString(),
                              icon: Icons.pending,
                              color: Colors.orange,
                              onTap: () => context.push('/maintenance?status=open'),
                            ),
                            StatCard(
                              title: 'In Progress',
                              value: dashboard.data.maintenanceIssues.inProgress.toString(),
                              icon: Icons.engineering,
                              color: Colors.blue,
                              onTap: () => context.push('/maintenance?status=in_progress'),
                            ),
                            StatCard(
                              title: 'Critical',
                              value: dashboard.data.maintenanceIssues.critical.toString(),
                              icon: Icons.priority_high,
                              color: Colors.red,
                              onTap: () => context.push('/maintenance?priority=critical'),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.largePadding),

                    // Recent Alerts
                    ConfigurablePermissionSection(
                      screenName: 'dashboard',
                      widgetName: 'alerts_summary',
                      title: 'Recent Alerts',
                      children: [
                        RecentAlertsWidget(alerts: dashboard.data.recentAlerts),
                      ],
                    ),
                    // Add bottom padding to prevent overflow with bottom navigation
                    const SizedBox(height: 80),
                  ],
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 64, color: Colors.red),
                      const SizedBox(height: AppConstants.defaultPadding),
                      Text(
                        'Failed to load dashboard',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        error.toString(),
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      ElevatedButton(
                        onPressed: () => ref.invalidate(dashboardDataProvider),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
              // Add bottom padding for all content to prevent overflow with bottom navigation
              const SizedBox(height: 80),
            ],
          ),
        ),
      ),
      bottomNavigationBar: RoleBasedBottomNavBar(
        currentIndex: 0,
        onTap: (index) => _handleBottomNavTap(context, index),
      ),
    );
  }

  void _handleLogout(WidgetRef ref) {
    ref.read(authStateProvider.notifier).logout();
  }

  void _handleBottomNavTap(BuildContext context, int index) {
    // Get the navigation items for the current user's role
    final routes = [
      '/dashboard',      // 0 - Dashboard
      '/properties',     // 1 - Properties
      '/maintenance',    // 2 - Maintenance
      '/attendance',     // 3 - Attendance
      '/admin',          // 4 - Admin
      '/profile',        // 5 - Profile (fallback)
    ];

    if (index < routes.length && index != 0) {
      context.push(routes[index]);
    }
  }
}
